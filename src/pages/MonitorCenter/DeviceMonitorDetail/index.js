/*
 * @LastEditors: @LastEditedBy
 * @LastEditTime: 2025-08-04 09:42:33
 * @FilePath: /水利厅运维/src/pages/MonitorCenter/DeviceMonitorDetail/index.js
 */

import React, { useEffect, useState, memo } from 'react';
import { Row, Col, Tabs, Descriptions, Icon, Menu, Dropdown, Empty } from 'antd';
import { connect, createShadow } from 'dryad';
import { Card, Button } from '@/components/PAntd';
import { Pie } from '@/pages/MonitorCenter/DeviceMonitorDetail/components/Charts';
import IndicatorModal from '@/pages/MonitorCenter/DeviceMonitorDetail/components/IndicatorModal/index';
import WorkingModal from '@/pages/MonitorCenter/DeviceMonitorDetail/components/WorkingModel/index';
import InfoModal from '@/pages/MonitorCenter/DeviceMonitorDetail/components/Information/index';
import PerformanceMonitor from '@/pages/MonitorCenter/DeviceMonitorDetail/components/PerformanceMonitor/index';
import styles from './index.less';
import BG from '@/pages/MonitorManage/bg1.jpg';
import NavTabs from '@/assets/navTabs.png';
import WorkingMonitor from './components/WorkingMonitor/index';
import LabelPerformanceMonitor from './components/LabelPerformanceMonitor';
import LogMonitor from '@/pages/MonitorCenter/DeviceMonitorDetail/components/logMonitor/index';
import OperationMonitor from '@/pages/MonitorCenter/DeviceMonitorDetail/components/operationMonitor/index';
import OperationMonitorMock from '@/pages/MonitorCenter/DeviceMonitorDetail/components/operationMonitor/indexMock';
import DeviceAlarm from './components/WorkingMonitor/components/DeviceAlarm';
import { smartDecrypt } from '@/utils/EncryptDecryp';

const { TabPane } = Tabs;

const DeviceMonitorDetail = props => {
  const {
    location: { query },
    dispatch,
    deviceMonitorDetail: {
      deviceListTrend = [],
      deviceListSelect = [],
      deviceWorkKpiByUser = [],
      deviceInfoByCiId = {},
      userDefaultKpiValue = [],
      deviceKpiWorkTrend = [],
      deviceInfoByModuleTypeCiId = [],
      alarmList = [],
    },
  } = props;

  const [key, setkey] = useState('性能监控');
  // console.log('queryyyyy',query)
  const getAlarmList = () => {
    dispatch({
      type: 'deviceMonitorDetail/getAlarmList',
      payload: {
        ciId: query.ciId,
      },
    });
  };

  const getKpiTree = (point = 1) => {
    dispatch({
      type: 'deviceMonitorDetail/getKpiTree',
      payload: {
        point,
      },
    });
  };

  const getTrend = node => {
    dispatch({
      type: 'deviceMonitorDetail/getDeviceKpiWorkTrend',
      payload: {
        jsonParam: JSON.stringify({ ...query, kpiList: node }),
      },
    });
  };

  const getUserDefaultKpiValue = () => {
    dispatch({
      type: 'deviceMonitorDetail/getUserDefaultKpiValue',
      payload: query,
    });
  };

  const saveUserDefaultWorkKpi = (params, callback) => {
    dispatch({
      type: 'deviceMonitorDetail/saveUserDefaultWorkKpi',
      payload: {
        jsonParam: JSON.stringify(params),
      },
      callback,
    });
  };

  const getDeviceWorkKpiByUser = () => {
    dispatch({
      type: 'deviceMonitorDetail/getDeviceWorkKpiByUser',
      payload: {},
    });
  };

  const getData = () => {
    // getKpiTree();
    // getKpiTree(0);
    dispatch({
      type: 'deviceMonitorDetail/getDeviceInfoByCiId',
      payload: query,
    });

    dispatch({
      type: 'deviceMonitorDetail/getDeviceInfoByModuleTypeCiId',
      payload: query,
    });
    // getDeviceWorkKpiByUser();
    // getUserDefaultKpiValue();
  };
  const [level, setLevel] = useState('');
  useEffect(() => {
    getData({ activeKey: '主机监控' });
    getAlarmList();
  }, []);
  const menu = (
    <Menu>
      <Menu.Item>
        <Button type="link" target="_blank" href={deviceInfoByCiId.connectAddr}>
          直连
        </Button>
      </Menu.Item>
      <Menu.Item>
        <Button type="link" target="_blank" href={deviceInfoByCiId.proxyAddr}>
          代理
        </Button>
      </Menu.Item>
    </Menu>
  );
  let tabs = [
    {
      name: '性能监控',
      tabPane: (
        // <LabelPerformanceMonitor
        //   type="performance"
        //   deviceInfoByModuleTypeCiId={deviceInfoByModuleTypeCiId}
        //   moduleType={query.moduleType}
        //   query={query}
        // ></LabelPerformanceMonitor>
        <WorkingMonitor
          type="performance"
          deviceInfoByModuleTypeCiId={deviceInfoByModuleTypeCiId}
          moduleType={query.moduleType}
          query={query}
        />
      ),
      color: '#5C76FB',
    },
    // {
    //   name: '工况监控',
    //   tabPane: (
    //     <WorkingMonitor
    //       type="work"
    //       deviceInfoByModuleTypeCiId={deviceInfoByModuleTypeCiId}
    //       moduleType={query.moduleType}
    //       query={query}
    //     />
    //   ),
    //   color: '#5C76FB',
    // },
    // {
    //   name: 'syslog日志监控',
    //   tabPane: <LogMonitor type="config" moduleType={query.moduleType} query={query} />,
    //   color: '#F9758A',
    // },
    // {
    //   name: '操作日志监控',
    //   tabPane: <OperationMonitor type="config" moduleType={query.moduleType} query={query} />,
    //   color: 'rgb(132,0,255)',
    // },
    // {
    //   name: '网元业务日志（CHR）管理',
    //   tabPane: <OperationMonitorMock type="config" moduleType={query.moduleType} query={query} />,
    //   color: 'rgb(132,0,255)',
    // },
    // { name: '变更记录', tabPane: <InfoModal query={query} />, color: '#38DFC5' },
    // { name: '设备告警', tabPane: <DeviceAlarm query={query} types="全部" />, color: '#B574FE' },
    // { name: '自动化操作', tabPane: <InfoModal query={query} />, color: '#5C76FB' },
  ];
  if (
    query.moduleType &&
    (query.modelName?.indexOf('数据库') > -1 ||
      query.pname?.indexOf('数据库') > -1 ||
      query.moduleType == 'VMS')
  ) {
    tabs = tabs.filter(item => item.name !== '工况监控');
  }
  return (
    <Row gutter={8} className={styles.deviceMonitorDetail} style={{ minHeight: '100%' }}>
      <Col span={4} style={{ height: '100%' }}>
        <div style={{ height: '100%', backgroundColor: '#fff', overflow: 'scroll' }}>
          <Card style={{ height: '240px' }}>
            <Pie status={deviceInfoByCiId.deviceStatus}></Pie>
            <div className={styles.time}>更新时间：{deviceInfoByCiId.updateTime}</div>
          </Card>
          <div style={{ height: '8px', backgroundColor: '#F0F2F5' }}></div>
          <Card
            title="资源信息"
            style={{ margin: '10px 0 0 0', height: 'calc(100%-240)' }}
            headStyle={{ marginBottom: '10px' }}
            bodyStyle={{ overflow: 'scroll' }}
          >
            {deviceInfoByModuleTypeCiId ? (
              <Descriptions column={1} size="small">
                {
                deviceInfoByModuleTypeCiId?.map(v => {
                  return v.fieldName == '开发运维联系方式' ? (
                    <Descriptions.Item label={v.fieldName}>
                     {
                        smartDecrypt(v.fieldValue) || '--'
                     }
                    </Descriptions.Item>
                  ) : (
                    <Descriptions.Item label={v.fieldName}>{v.fieldValue || '--'}</Descriptions.Item>
                  )
                }
                )}
              </Descriptions>
            ) : (
              <Empty></Empty>
            )}
          </Card>
        </div>
      </Col>
      <Col span={20} style={{ minHeight: '100%' }}>
        {/* <Card
          title="告警信息"
          style={{ height: 'auto', position: 'relative' }}
          bodyStyle={{ height: 98, overflow: 'auto' }}
        >
          <div className={styles.navTabs}>
            <div className={styles.level} onClick={()=>{setLevel('1')}}>
              <span
                style={{ width: 10, height: 10, borderRadius: '50%', background: '#FA7C58' }}
              ></span>
              一级告警
            </div>
            <div className={styles.level} onClick={()=>{setLevel('2')}}>
              <span
                style={{ width: 10, height: 10, borderRadius: '50%', background: '#FA7087' }}
              ></span>
              二级告警
            </div>
            <div className={styles.level} onClick={()=>{setLevel('3')}}>
              <span
                style={{ width: 10, height: 10, borderRadius: '50%', background: '#FCC034' }}
              ></span>
              三级告警
            </div>
            <div className={styles.level} onClick={()=>{setLevel('4')}}>
              <span
                style={{ width: 10, height: 10, borderRadius: '50%', background: '#5C76FB' }}
              ></span>
              四级告警
            </div>
          </div>

          <div className={styles.info}>
            {alarmList.map(item => {
              return !level||level==item.level?
               (
              <div
                className={
                  item.level == '1'
                    ? styles.infoCont1
                    : item.level == '2'
                    ? styles.infoCont2
                    : item.level == '3'
                    ? styles.infoCont3
                    : styles.infoCont4
                }
                style={{ width: 'auto', padding: '0 5px' }}
              >
                {item.message}
              </div>
            ):''})}
          </div>
        </Card>
         */}
        <Card
          style={{ height: 110, height: '100%' }}
          className={styles.pan}
        >
          {/* <Tabs activeKey={key} onChange={setkey} size="small" animated={false}>
            {tabs.map(v => (
              <TabPane
                key={v.name}
                // forceRender={true}
                tab={
                  <span className={styles.navSpan}>
                    <div className={styles.navTitle} style={{ background: `${v.color}` }}>
                      <img src={NavTabs} alt="" />
                    </div>
                    <div>{v.name}</div>
                  </span>
                }
              >
                {/*  <TabContent></TabContent>  * /}
                <div style={{ overflow: 'scroll-y', maxHeight: 517 }}>
                  <Card bodyStyle={{ minHeight: 517 }}>{v.tabPane}</Card>
                </div>
              </TabPane>
            ))}
          </Tabs> */}
          <div style={{ overflow: 'scroll-y', maxHeight: 517 }}>
            <Card bodyStyle={{ minHeight: 517 }}>{tabs[0].tabPane}</Card>
          </div>
        </Card>
      </Col>
    </Row>
  );
};

export default connect(
  createShadow((state, ownProps) => {
    // 此处可根据state(即model数据)和props(即组件自身props)手动生成并返回类似上一中方式的数据, 如：
    return {
      deviceMonitorDetail: `deviceMonitorDetail${ownProps.match.params.ciId}`,
    };
  }),
  ({ deviceMonitorDetail }) => ({
    deviceMonitorDetail,
  }),
)(DeviceMonitorDetail);
