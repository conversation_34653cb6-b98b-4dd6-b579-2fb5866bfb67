import React, { memo, useState, useRef, useEffect, useMemo } from 'react';
import { Button, message, Modal, Spin } from 'antd';
import { connect } from 'dryad';
import { withContext } from 'demasia-pro-layout';
import { getAction } from '@/utils/utils';
import downloadFile from '@/utils/download';
import CustomForm from './CustomForm';
import CuatomFormWrapper from './CustomForm/CuatomFormWrapper';
import HistoryRecord from './HistoryRecord';
import HistoryFileRecord from './HistoryFileRecord';
import HandleForm from './HandleForm';
import styles from './index.less';
import moment from 'moment';
import Notification from './Notification';

const OrderCreate = memo(props => {
  const {
    dispatch,
    location = {},
    _closeSingleTab,
    _tabKey,
    loading,
    actionLoading = false,
    submitLoading = false,
  } = props;

  const { query = {} } = location;

  const action = useMemo(() => {
    const { url, params } = props.match;
    return { type: getAction(url, 'shuiliOrder_'), params };
  }, []);

  // view | add | handle | edit
  const type = useMemo(() => {
    if (action.type === 'handle' && query.orderState === '新建') return 'edit';
    return action.type;
  }, [action, query]);

  const formRef = useRef(); // 表格表单
  const handleFormRef = useRef(); // 处理表单
  const submitRef = useRef(); // 提交表单
  const notificationRef = useRef(); // 通知方式


  const [baseInfo, setBaseInfo] = useState({
    taskDefKey: '',
    taskDefName: '',
    draftProcInstId: '',
  });
  const [noticeList, setNoticeList] = useState([]);//通知方式
  const [attrList, setAttrList] = useState([]);
  const [fileRecordList, setFileRecordList] = useState([]);
  const [exportLoading, setExportLoading] = useState(false);
  const [temporaryExportLoading, setTemporaryExportLoading] = useState(false);

  const getAttrCode = name => {
    return attrList.find(v => v.attrasName === name)?.attrCode ?? 'default_field';
  };
  const columnRecord = useMemo(() => {
    return attrList
      .filter(v => v.attrasName !== '列表数据')
      .reduce((pre, cur) => {
        pre[cur.attrCode] = cur.attrValue ?? undefined;
        return pre;
      }, {});
  }, [attrList]);

  const queryOrderTaskAttribute = (orderFlowHistory = []) => {
    dispatch({
      type: 'orderFlowDetail/queryOrderTaskAttribute',
      payload: { orderType: query.orderType },

      callback: res => {
        if (res.code !== 200) return message.error(res.msg);

        const orderFlowAttribute = res.resData?.attrData ?? [];
        const taskDefKey = orderFlowAttribute[0]?.taskDefKey;
        const taskDefName = orderFlowAttribute[0]?.taskDefName;

        const flowAttrList = orderFlowAttribute[0]?.attrList ?? [];

        const nowDate = moment();
        const year = nowDate.year();
        const month = nowDate.month() + 1;
        const date = nowDate.date();

        const attrList = flowAttrList.map(flow => {
          const attr = orderFlowHistory.find(history => flow.attrCode === history.attrCode) ?? flow;

          // if (action.type === 'add') {
          //   switch (flow.attrasName) {
          //     case '年':
          //       attr.attrValue = year;
          //       break;
          //     case '月':
          //       attr.attrValue = month;
          //       break;
          //     case '日':
          //       attr.attrValue = date;
          //       break;
          //   }
          // }
          return attr;
        });

        setAttrList(attrList);
        setBaseInfo({
          taskDefineKey: taskDefKey,
          taskName: taskDefName,
          draftProcInstId: `DRAFT${new Date().getTime()}`,
        });
      },
    });
  };

  const queryOrderHistoryInfo = () => {
    return new Promise(reslove => {
      dispatch({
        type: 'orderHandle/queryOrderHistoryInfo',
        payload: {
          procInstId: query.procInstId,
          orderType: query?.orderType,
        },
        callback: res => {
          if (res.code !== 200) message.error(res.msg);
          reslove(res.resData?.table ?? []);
          setFileRecordList(res.resData?.file ?? []);
        },
      });
    });
  };

  const getOrderList = async () => {
    let promise = () => Promise.resolve();
    if (['handle', 'view'].includes(action.type)) {
      promise = queryOrderHistoryInfo;
    }

    const history = await promise();
    queryOrderTaskAttribute(history);
  };

  useEffect(() => {
    getOrderList();
  }, []);

  // 暂存order - 暂时取消该功能
  // const saveOrder = () => {
  //   const values = formRef.current?.getFieldsValue();
  //   const { fieldList = [], ...params } = values;
  //   params[getAttrCode('列表数据')] = JSON.stringify(
  //     Object.keys(fieldList).map(key => fieldList[key]),
  //   );

  //   const { draftProcInstId, ...info } = baseInfo;
  //   dispatch({
  //     type: 'orderHandle/saveOrderInfo',
  //     payload: {
  //       deploymentId: '',
  //       processAlias: query?.processAlias,
  //       orderType: query?.orderType,
  //       orderLevel: query?.orderLevel,
  //       nodeId: query?.nodeId,
  //       orderRegion: query?.flowName,
  //       procInstId: query?.procInstId ?? draftProcInstId,
  //       ...info,
  //       ...params,
  //     },
  //     callback: res => {
  //       if (res.code !== 200) return message.error(res.msg);
  //       message.success(res.msg || '保存成功');
  //       _closeSingleTab(_tabKey);
  //     },
  //   });
  // };

  const cancleSubmit = () => {
    Modal.confirm({
      title: '取消确认',
      content: '取消后工单信息不保存，是否取消？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        _closeSingleTab(_tabKey, false, () => { });
      },
      onCancel: () => { },
    });
  };

  const handleSubmit = async () => {
    try {
      const { noticeType } = notificationRef?.current?.getFieldsValue();
      const params = await submitRef?.current?.getSubmitParams();
      if (!params) {
        return;
      }
      if(!noticeType.length){
        return 
      }

      const typeMap = {
        add: 'orderHandle/startProcess',
        edit: 'ShuiliHandleOrder/updateStartProcess',
      };

      const { draftProcInstId, ...info } = baseInfo;

      // 创建FormData对象
      const formData = new FormData();
      const formParams = {
        deploymentId: '',
        processAlias: query?.processAlias,
        orderType: query?.orderType,
        orderLevel: query?.orderLevel,
        nodeId: query?.nodeId,
        orderRegion: query?.flowName,
        procInstId: query?.procInstId ?? draftProcInstId,
        ...info,
        ...params,
        file: undefined,
      };
      formData.append('params', JSON.stringify(formParams));

      if (params?.file?.[0]?.originFileObj) {
        formData.append('file', params?.file?.[0]?.originFileObj);
      }

      dispatch({
        type: typeMap[type],
        payload: formData,
        callback: res => {
          if (res.code !== 200) return message.error(res.msg);
          message.success(res.msg || '提交成功');
          _closeSingleTab(_tabKey);
        },
      });
    } catch (error) {
      console.log('form validate error:', error);
    }
  };

  const handleExport = async () => {
    try {
      setExportLoading(true);
      await downloadFile({
        url: `/api/order/orderProcess/exportWord?procInstId=${query.procInstId}`,
      });
    } finally {
      setExportLoading(false);
    }
  };

  const temporaryExport = async () => {
    try {
      setTemporaryExportLoading(true);
      const params = await submitRef?.current?.getSubmitParams(true);
      const downloadParams = {
        orderType: query?.orderType,
        ...params,
        file: undefined,
      };

      await downloadFile({
        url: `/api/order/orderProcess/unSubmitExport`,
        param: JSON.stringify(downloadParams),
        requestType: 'json',
      });
    } finally {
      setTemporaryExportLoading(false);
    }
  };

  const handleNextProcess = async (type, annotations) => {
    dispatch({
      type: 'ShuiliHandleOrder/handleOrderProcess',
      payload: {
        taskDefineKey: query.taskDefineKey,
        orderType: query.orderType,
        taskName: query.taskName,
        taskId: query.taskId,
        procInstId: query.procInstId,
        operType: type,
        annotations,
        orderId: query?.id,
      },
      callback: res => {
        if (res.code !== 200) return message.error(res.msg);

        message.success(res.msg || '提交成功');
        _closeSingleTab(_tabKey);
      },
    });
  };

  const addFile = fileList => {
    if (!fileList?.length) return Promise.resolve();

    return new Promise(resolve => {
      const formData = new FormData();
      formData.append('file', fileList[0]?.originFileObj);
      formData.append('procInstId', query.procInstId);
      formData.append('taskDefineKey', query.taskDefineKey);
      formData.append('taskId', query.taskId);

      dispatch({
        type: 'ShuiliHandleOrder/addFile',
        payload: formData,
        callback: res => {
          if (res.code !== 200) return message.error(res.msg);
          resolve();
        },
      });
    });
  };

  const handleNext = async type => {
    const { annotations, fileList } = handleFormRef.current?.getFieldsValue();

    await addFile(fileList);
    await handleNextProcess(type, annotations);
  };

  const handleReject = () => {
    Modal.confirm({
      title: '提示',
      content: '是否确认驳回工单，将关闭工单',
      onOk() {
        handleNext('驳回');
      },
    });
  };
  const handleBack = () => {
    Modal.confirm({
      title: '提示',
      content: '是否确认退回工单，上一步骤操作者可修改工单',
      onOk() {
        handleNext('退回');
      },
    });
  };

  return (
    <div className={styles.createOrder}>
      <div className={styles.content}>
        <Spin spinning={loading}>
          <div style={{ width: 960, margin: '16px auto', minHeight: 260 }}>
            {attrList.length > 0 && (
              <CuatomFormWrapper wrappedComponentRef={formRef}>
                <CustomForm
                  type={type}
                  record={columnRecord}
                  getAttrCode={getAttrCode}
                  ref={submitRef}
                  customFormType={query.flowName || query.processAlias}
                  attrList={attrList}
                  fileRecordList={fileRecordList}
                ></CustomForm>
              </CuatomFormWrapper>
            )}
          </div>
        </Spin>

        {['handle', 'view'].includes(type) && <HistoryRecord type={type} {...query} />}

        {['handle'].includes(type) && (
          <div style={{ paddingTop: 16 }}>
            <HandleForm wrappedComponentRef={handleFormRef} {...query} />
          </div>
        )}
        {['handle', 'view'].includes(type) && <HistoryFileRecord type={type} {...query} />}
      </div>

      <footer className={styles.footer}>
        {/* 通知方式 */}
        {
          ['add', 'edit', 'handle','view'].includes(type) ? (
            <div style={{ marginRight: 10 }} className={styles.notification}>
              <Notification
                type={type}
                wrappedComponentRef={notificationRef}
                initialValues={null}
              />
            </div>
          ) : ""
        }
        {['add', 'edit'].includes(type) ? (
          <>
            <Button loading={submitLoading} onClick={() => cancleSubmit()}>
              取消
            </Button>
            {/* <Button onClick={() => saveOrder()}>保存</Button> */}
            {['ZJSSL', 'ZJSSS']?.includes(query.orderType) && (
              <Button
                onClick={() => temporaryExport()}
                type="primary"
                loading={temporaryExportLoading}
              >
                导出工单
              </Button>
            )}

            <Button loading={submitLoading} type="primary" onClick={() => handleSubmit()}>
              提交
            </Button>
          </>
        ) : (
          <Button loading={exportLoading} type="primary" onClick={() => handleExport()}>
            导出工单
          </Button>
        )}

        {'handle' === type && (
          <>
            <Button loading={actionLoading} type="primary" onClick={() => handleNext('通过')}>
              通过
            </Button>
            <Button loading={actionLoading} type="primary" onClick={() => handleReject()}>
              驳回
            </Button>
            <Button loading={actionLoading} type="primary" onClick={() => handleBack()}>
              退回
            </Button>
          </>
        )}
      </footer>
    </div>
  );
});

const config = [
  ['closeSingleTab', 'tabKeys', 'tabKey'],
  ['_closeSingleTab', '_tabKeys', '_tabKey'],
];

const mapStateToProps = ({ loading }) => {
  return {
    loading: loading.effects['orderFlowDetail/queryOrderTaskAttribute'],
    actionLoading: loading.effects['ShuiliHandleOrder/handleOrderProcess'],
    submitLoading:
      loading.effects['orderHandle/startProces'] ||
      loading.effects['ShuiliHandleOrder/updateStartProcess'],
  };
};

export default connect(mapStateToProps)(withContext(...config)(OrderCreate));
