import React, { forwardRef, useImperativeHandle } from 'react';
import { Form, Checkbox } from 'antd';

const Index = forwardRef((props, ref) => {
        const {
            form,
            form: { getFieldDecorator },
            initialValues,
            type,
        } = props;

        useImperativeHandle(ref, () => form);
        return(
            <Form layout="inline" >
            <Form.Item label="提醒发方式">
                {getFieldDecorator('noticeType', {
                    initialValue: initialValues?.noticeType || ['1'],
                    rules: [{ required: true, message: '请选择提醒发方式' }],
                })(
                    <Checkbox.Group disabled={type === 'view'}>
                        <Checkbox value="1">短信</Checkbox>
                        <Checkbox value="2">九龙钉</Checkbox>
                    </Checkbox.Group>,
                )}
            </Form.Item>
            </Form>
        )
})

export default Form.create()(Index);