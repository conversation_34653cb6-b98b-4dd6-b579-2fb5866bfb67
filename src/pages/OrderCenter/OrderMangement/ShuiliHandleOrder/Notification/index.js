import react, { memo, useState, useEffect } from 'react';
import { Card, Col, Form, Row, Table, Radio, Checkbox } from 'antd';
const Index=(props)=>{
    const {
        form: { getFieldDecorator },
        form,
        initialValues,
        type,
    } = props;
    return(
        <Form layout="inline" > 
        <Form.Item label="提醒发方式">
            {getFieldDecorator('noticeType', {
                initialValue: initialValues?.noticeType || ['1'],
                rules: [{ required: true, message: '请选择提醒发方式' }],
            })(
                <Checkbox.Group >
                    <Checkbox   value="1">短信</Checkbox>
                    <Checkbox value="2">九龙钉</Checkbox>
                </Checkbox.Group>,
            )}
        </Form.Item>
        </Form>
    )
}
export default Form.create()(Index);