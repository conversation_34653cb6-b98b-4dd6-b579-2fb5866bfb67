import React, { Fragment, memo, useEffect, useImperativeHandle, useState, forwardRef } from 'react';
import { Button, Input, Select, message } from 'antd';
import request from '@/utils/request';
import CustomTable from '../CustomTable';
import { FormItem } from './FormItem';
import styles from './index.less';
import SignAndDate from './SignAndDate';
import { v4 as uuidv4 } from 'uuid';

import {
  transFormArrToSelectOption,
  maxStrReg,
  chinesPattern,
  phonePattern,
  emailPattern,
} from './utils';

const defaultDataSouce = Array.from({ length: 3 }).map(() => ({
  key: uuidv4(),
  escIp: '',
  operSystem: '',
  usage: '',
}));

const OrderCreate = forwardRef((props, ref) => {
  const { form, getAttrCode, type, record = {} } = props;
  const [roomEnumList, setRoomEnumList] = useState([]);
  const [businessSystemList, setBusinessSystemList] = useState([]);

  const [dataSouce, setDataSouce] = useState(defaultDataSouce);
  const isView = type === 'view' || type === 'handle';
  const editDataSourse = record[getAttrCode('申请权限')]
    ? JSON.parse(record[getAttrCode('申请权限')])
    : {};
  const handleAdd = () => {
    setDataSouce([
      ...dataSouce,
      {
        key: uuidv4(),
        escIp: '',
        operSystem: '',
        usage: '',
      },
    ]);
  };

  const handleRemove = row => {
    setDataSouce(preDataSource => preDataSource.filter(v => v.key !== row.key));
  };

  const defaultProps = type => {
    return { type, form, disabled: isView };
  };

  const getRoomEnumList = async () => {
    const res = await request('/api/order/orderProcess/getRoomEnumList');
    const list = (res.resData ?? []).map(v => ({ value: v, label: v }));
    setRoomEnumList(list);
  };

  const getBusinessSystemList = async () => {
    const res = await request('/api/order/orderProcess/getWebOrInfoSysnameList');
    const list = (res.resData ?? []).map(v => ({ value: v, label: v }));
    setBusinessSystemList(list);
  };

  const getSubmitParams = () => {
    return new Promise((resolve, reject) => {
      form.validateFields((errors, values) => {
        if (errors) {
          reject(errors);
          return;
        }

        // 获取表单数据
        const { fieldList = {}, ...otherValues } = values;

        // 转换成数组并过滤掉没有填写用途的行
        const validRows = Object.keys(fieldList)
          .map(key => ({
            key,
            ...fieldList[key],
          }))
          .filter(row => row.escIp || row.operSystem || row.usage);

        // 检查是否至少填写了一行用途
        if (validRows.length === 0) {
          message.error('请至少填写一行申请权限');
          reject(new Error('请至少填写一行申请权限'));
          return;
        }

        // 检查已填写用途的行是否完整填写了操作系统和使用方式
        const incompleteRow = validRows.find(row => {
          // 检查操作系统和使用方式是否都填写了
          return !row.operSystem || !row.usage || !row.escIp;
        });

        if (incompleteRow) {
          message.error('有ECS IP地址、操作系统或使用方式未填写完整');
          reject(new Error('有ECS IP地址、操作系统或使用方式未填写完整'));
          return;
        }

        // 构建提交数据
        const submitData = {
          ...otherValues,
          [getAttrCode('申请权限')]: JSON.stringify(validRows),
          [getAttrCode('账号所属区域')]: values?.[getAttrCode('账号所属区域')]?.join(','),
        };

        resolve(submitData);
      });
    });
  };

  useImperativeHandle(ref, () => ({
    getSubmitParams,
  }));

  useEffect(() => {
    setDataSouce(editDataSourse?.length > 0 ? editDataSourse : [...defaultDataSouce]);
  }, [JSON.stringify(editDataSourse)]);

  useEffect(() => {
    getRoomEnumList();
    getBusinessSystemList();
  }, []);

  const dynamicColumns = [
    {
      label: 'ECS IP地址',
      dataIndex: 'escIp',
      col: 8,
      required: true,
      render(text, row, rowIdx) {
        return (
          <FormItem
            {...defaultProps('textarea')}
            field={`fieldList[${row.key}].escIp`}
            initialValue={row.escIp}
            required={false}
          />
        );
      },
    },
    {
      label: '操作系统',
      dataIndex: 'operSystem',
      col: 8,
      required: true,
      render(text, row, rowIdx) {
        return (
          <FormItem
            {...defaultProps('textarea')}
            field={`fieldList[${row.key}].operSystem`}
            initialValue={row.operSystem}
            required={false}
            rules={maxStrReg(100)}
          />
        );
      },
    },
    {
      label: '使用方式（RDP、SSH、SFTP）',
      dataIndex: 'usage',
      col: 8,
      required: true,
      render(text, row, rowIdx) {
        return (
          <>
            <FormItem
              {...defaultProps('textarea')}
              field={`fieldList[${row.key}].usage`}
              initialValue={row.usage}
              required={false}
              rules={maxStrReg(10)}
            />
            {!isView && (
              <span className={styles.tableAction} onClick={e => e.stopPropagation()}>
                {dataSouce.length > 1 && (
                  <Button title="删除" type="link" icon="minus" onClick={() => handleRemove(row)} />
                )}
                {rowIdx === dataSouce.length - 1 && (
                  <Button title="添加" type="link" icon="plus" onClick={() => handleAdd()} />
                )}
              </span>
            )}
          </>
        );
      },
    },
  ];

  const formConfig = {
    dataSouce,
    record,
    customColumns: [
      {
        label: '',
        children: [
          {
            label: '申请处室（单位）',
            labelCol: 6,
            valueCol: 10,
            dataIndex: 'applicationOffice',
            required: true,
            render() {
              return (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ width: '80%' }}>
                    <FormItem
                      {...defaultProps('select')}
                      required={true}
                      field={getAttrCode('申请处室')}
                      initialValue={record[getAttrCode('申请处室')]}
                      options={roomEnumList}
                      showSearch
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    />
                  </div>

                  <div>（盖章）</div>
                </div>
              );
            },
          },
          {
            label: '申请人',
            labelCol: 3,
            valueCol: 5,
            dataIndex: 'appler',
            required: true,
            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={getAttrCode('申请人')}
                  initialValue={record[getAttrCode('申请人')]}
                  rules={chinesPattern(10)}
                />
              );
            },
          },
        ],
      },
      {
        label: '',
        children: [
          {
            label: '申请账号',
            labelCol: 6,
            valueCol: 6,
            dataIndex: 'applicationAcount',
            required: true,
            render() {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  required={true}
                  field={getAttrCode('申请账号')}
                  initialValue={record[getAttrCode('申请账号')]}
                  rules={maxStrReg(20)}
                />
              );
            },
          },
          {
            label: '初始密码',
            labelCol: 4,
            valueCol: 8,
            dataIndex: 'initPassword',
            required: true,
            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={getAttrCode('初始密码')}
                  initialValue={record[getAttrCode('初始密码')]}
                  rules={maxStrReg(20)}
                />
              );
            },
          },
        ],
      },
      {
        label: '账号所属区域',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'zhssqy',
        required: true,
        render() {
          return (
            <FormItem
              {...defaultProps('checkBox')}
              field={getAttrCode('账号所属区域')}
              initialValue={record[getAttrCode('账号所属区域')]?.split(',')}
              options={transFormArrToSelectOption(['公有云、专有云', '信创云'])}
            />
          );
        },
      },
      {
        label: '申请用途',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'sqyt',
        required: true,
        render() {
          return (
            <FormItem
              {...defaultProps('textarea')}
              required={true}
              field={getAttrCode('申请用途')}
              initialValue={record[getAttrCode('申请用途')]}
              rules={maxStrReg(50)}
            />
          );
        },
      },
      {
        label: '业务系统',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'ywst',
        required: true,
        render() {
          return (
            <FormItem
              {...defaultProps('select')}
              required={true}
              field={getAttrCode('业务系统')}
              initialValue={record[getAttrCode('业务系统')]}
              options={businessSystemList}
              showSearch
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          );
        },
      },

      {
        label: '申请权限',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'applyTable',
        required: true,
        rowHeight: 'auto',
        valueColClassName: 'valueColTd',
        render() {
          return (
            <CustomTable
              config={{
                normalColumns: dynamicColumns,
                dataSouce: dataSouce,
              }}
              rowHeight={50}
              headHeight={50}
              classNameStr="childrenTable"
            />
          );
        },
      },
      {
        label: '',
        children: [
          {
            label: '账号使用人',
            labelCol: 6,
            valueCol: 6,
            dataIndex: 'acountUser',
            required: true,
            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  required={true}
                  field={getAttrCode('账号使用人')}
                  initialValue={record[getAttrCode('账号使用人')]}
                  rules={chinesPattern(10)}
                />
              );
            },
          },
          {
            label: '单位（公司）',
            labelCol: 6,
            valueCol: 6,
            dataIndex: 'unit',
            required: true,
            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={getAttrCode('单位（公司）')}
                  initialValue={record[getAttrCode('单位（公司）')]}
                  rules={chinesPattern(20)}
                />
              );
            },
          },
        ],
      },
      {
        label: '',
        children: [
          {
            label: '手机',
            labelCol: 6,
            valueCol: 6,
            dataIndex: 'phone',
            required: true,
            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  required={true}
                  field={getAttrCode('手机')}
                  initialValue={record[getAttrCode('手机')]}
                  rules={phonePattern}
                />
              );
            },
          },
          {
            label: '邮箱',
            labelCol: 6,
            valueCol: 6,
            dataIndex: 'email',
            required: true,
            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={getAttrCode('邮箱')}
                  initialValue={record[getAttrCode('邮箱')]}
                  rules={emailPattern}
                />
              );
            },
          },
        ],
      },
      {
        label: '堡垒机账号使用安全承诺',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'applyPurpose',
        required: false,
        rowHeight: 140,
        render() {
          return (
            <Fragment>
              <FormItem
                {...defaultProps('textarea')}
                field={`${getAttrCode('堡垒机账号使用安全承诺')}.content`}
                initialValue={
                  '本人自觉遵守国家网络和信息安全法律、法规，承诺该账号只用于相应信息系统更新维护，不违规部署其他无关应用系统，不违规上传和留存敏感信息，不违规转让账号使用权。若本人利用该账号从事危害网络和系统安全的活动，愿承担全部责任。'
                }
                style={{ textAlign: 'left' }}
                disabled={true}
              />
              <SignAndDate
                field={getAttrCode('堡垒机账号使用安全承诺')}
                record={record}
                defaultProps={defaultProps}
                getAttrCode={getAttrCode}
              />
            </Fragment>
          );
        },
      },
    ],
  };

  return (
    <div className={styles.customForm}>
      <div className={styles.title}>浙江省水利厅政务云堡垒机账号申请表</div>
      <CustomTable
        config={formConfig}
        style={{ paddingTop: 16 }}
        rowHeight={70}
        headHeight={70}
      ></CustomTable>
      {/* 注释 */}
      <div>222</div>
    </div>
  );
});

export default memo(OrderCreate);
