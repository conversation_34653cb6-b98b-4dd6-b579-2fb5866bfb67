.OrderCreateEntry {
  height: 100%;
  display: flex;
  gap: 12px;
  .orderTree {
    width: calc((5 / 24) * 100%);
    height: 100%;
  }
  .cardList {
    flex: 1;
    height: 100%;
  }
  :global {
    .ant-tree-treenode-switcher-close {
      .ant-tree-node-content-wrapper-normal {
        width: calc(100% - 24px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.createOrder {
  background-color: #fff;
  min-height: 100%;
  .content {
    min-height: 100%;
    box-sizing: border-box;
    padding: 16px 16px calc(16px + 80px);

    .tableAction {
      position: absolute;
      left: calc(100% + 10px);
      color: #1890ff;
      width: 70px;
      text-align: left;
      bottom: 10px;
    }
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 80px;
    padding: 16px;
    z-index: calc(1999 + 1000);
    box-sizing: border-box;
    background-color: #fff;
    border: 2px solid #f0f2f5;
    :global {
      .ant-btn:not(:nth-last-child(1)) {
        margin-right: 12px;
      }
    }
  }
}
.notification{
  :global{
    .ant-form-inline .ant-form-item{
      display: flex;
      align-items: center;
    }
    .ant-checkbox-group{
      display: flex;
      align-items: center;
    }
  }
}