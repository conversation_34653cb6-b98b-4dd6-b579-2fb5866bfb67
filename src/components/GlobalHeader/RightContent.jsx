/*
 * @Descripttion:
 * @version:
 * @LastEditors: @LastEditedBy
 * @Date: 2022-10-24 15:01:44
 * @LastEditTime: 2025-08-04 09:45:43
 */
import { Icon, Tooltip, Tag } from 'antd';
import React, { useEffect } from 'react';
import { connect } from 'dryad';
import { goPage } from '@/utils/openTab';

// import { ConnectProps, ConnectState } from '@/models/connect';
import {
  NavAndHeaderTheme,
  NavLayoutMode,
  HeaderProps,
  useContextSelector,
} from 'demasia-pro-layout';
import Avatar from './AvatarDropdown';
// import HeaderSearch from '../HeaderSearch';
// import SelectLang from '../SelectLang';
import styles from './index.less';

const ENVTagColor = {
  dev: 'orange',
  test: 'green',
  pre: '#87d068',
};

const GlobalHeaderRight = props => {
  const { theme, layout } = props;
  let className = styles.right;
  const isMobile = useContextSelector(v => v.isMobile);

  if (theme === 'dark' && layout === 'topmenu' && !isMobile) {
    className = `${styles.right}  ${styles.dark}`;
  }
  return (
    <div className={className}>
      {/* <HeaderSearch
        className={`${styles.action} ${styles.search}`}
        placeholder="站内搜索"
        defaultValue="ponshine ui"
        dataSource={['搜索提示一', '搜索提示二', '搜索提示三']}
        onSearch={() => {}}
        onPressEnter={() => {}}
      /> */}
      {/* <Tooltip title="使用文档">
        <a
          target="_blank"
          href="http://pro.demasia.org/docs/getting-started"
          rel="noopener noreferrer"
          className={styles.action}
        >
          <Icon type="question-circle-o" />
        </a>
      </Tooltip> */}

      <span className={styles.action} onClick={() => window.open('http://*************:8000/#/home')}>
        <Icon type="radar-chart" />
        安全运营态势大屏
      </span>
      <span className={styles.action} onClick={() => goPage('/screen/index')}>
        <Icon type="bar-chart" />
        运维大屏
      </span>
      <Avatar />
      {REACT_APP_ENV && <Tag color={ENVTagColor[REACT_APP_ENV]}>{REACT_APP_ENV}</Tag>}
      {/* <SelectLang className={styles.action} /> */}
    </div>
  );
};

export default connect(({ settings }) => ({
  // theme: settings.navTheme,
  // layout: settings.navLayoutMode,
  theme: settings.navAndHeaderTheme,
  layout: settings.navLayoutMode,
}))(GlobalHeaderRight);
