:global {
  // *::-webkit-scrollbar {
  //   display: none; /* Chrome Safari */
  // }

  // * {
  //   scrollbar-width: none; /* firefox */
  //   -ms-overflow-style: none; /* IE 10+ */
  // }

  ::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  ::-webkit-scrollbar:vertical {
    display: none;
    /* Chrome Safari */
  }

  .demasia-pl-side-menu-side.demasia-pl-fixed-side-bar .demasia-pl-side-menu-container::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

// :global {
//   .demasia-pl-side-menu-logo {
//     background: linear-gradient(to right, #005be2, #44a2fd) !important;
//     //background: #005BE2;
//     line-height: 60px;

//     // padding: 0;
//     img {
//       height: 50px;
//       // height: auto;
//     }
//   }

//   .demasia-pl-side-menu-wrapper {
//     background: #fff;
//   }

//   .ant-menu-dark,
//   .ant-menu-dark .ant-menu-sub {
//     color: rgba(0, 0, 0, 0.7) !important;
//     background: #fff !important;
//   }

//   .ant-menu-dark .ant-menu-item,
//   .ant-menu-dark .ant-menu-item-group-title,
//   .ant-menu-dark .ant-menu-item>a {
//     color: rgba(0, 0, 0, 0.7) !important;
//   }

//   .ant-menu-dark .ant-menu-item:hover,
//   .ant-menu-dark .ant-menu-item-active,
//   .ant-menu-dark .ant-menu-submenu-active,
//   .ant-menu-dark .ant-menu-submenu-open,
//   .ant-menu-dark .ant-menu-submenu-selected,
//   .ant-menu-dark .ant-menu-submenu-title:hover {
//     color: rgba(0, 0, 0, 0.7) !important;
//   }

//   .ant-menu-dark .ant-menu-inline.ant-menu-sub {
//     box-shadow: none !important;
//   }

//   .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
//   .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
//   .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before {
//     color: rgba(0, 0, 0, 0.7) !important;
//   }

//   .ant-menu-submenu-arrow::before {
//     background: rgba(0, 0, 0, 0.7) !important;
//   }

//   .ant-menu-submenu-arrow::after {
//     background: rgba(0, 0, 0, 0.7) !important;
//   }

//   .ant-layout-sider-children {
//     background: #fff;
//   }

//   .ant-menu.ant-menu-dark .ant-menu-item-selected,
//   .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
//     background: #e6f6ff;
//   }

//   .ant-menu-dark .ant-menu-item-selected .anticon {
//     color: #1c77ed;
//   }

//   .ant-menu-dark .ant-menu-item-selected .anticon+span {
//     color: #1c77ed;
//   }

// }

:global {
  .demasia-pl-side-menu-logo {
    background: #003a8c;
    // padding-left: 2px;
 // padding-right: 2px;

    h1 {
      font-size: 14px;
      text-align: left;
    }
  }

  .ant-layout-sider {
    background: linear-gradient(#003a8c, #69c0ff);
  }

  .ant-btn-primary {
    background: #448be3;
  }

  .demasia-pl-side-menu-wrapper,
  .ant-menu-dark,
  .ant-menu-dark .ant-menu-sub {
    background: transparent;
  }

  .ant-menu.ant-menu-dark .ant-menu-item-selected,
  .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
    background: #1069d6;
  }

  .ant-menu-submenu-popup .ant-menu-sub {
    background: #2c7ee2;
  }

  .ant-menu-dark .ant-menu-inline.ant-menu-sub {
    background: #2c7ee2;
    box-shadow: 0 0 0px #1068d2 inset;
    //box-shadow:
  }

  .demasia-pl-side-menu-logo i {
    display: none;
  }

  .ant-btn-primary {}
}
}