/*
 * @Descripttion:
 * @version:
 * @LastEditors: @LastEditedBy
 * @Date: 2022-10-24 15:01:42
 * @LastEditTime: 2025-08-04 08:54:46
 */
/**
 * Demasia Pro v4 use `demasia-pro-layout` to handle Layout.
 * You can view component api by:
 * http://git.ponshine.fe/demasia/demasia-pro-layout
 */
import ProLayout, { Toolt<PERSON>, SettingDrawer, FixedHeightContent } from 'demasia-pro-layout';
import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'dryad';
import { Icon, ConfigProvider,Badge } from 'antd';
import RightContent from '@/components/GlobalHeader/RightContent';
import MenuBadgeManager from '@/components/MenuBadgeManager';
import projectSettings from '../../config/projectSettings';
import './BasicLayout.less';
// import title1 from '@/assets/title.png';

const BasicLayout = props => {
  const { dispatch, children, settings, ...needProps } = props;
  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      });
    }
  }, []);
  return (
    <Fragment>
      <ProLayout
        logo={projectSettings.logo}
        title={projectSettings.title}
        footerRender={false}
        homeIcon={false}
        headerRightContentRender={() => <RightContent />}
        settings={{
          ...settings,
          navLayoutMode: 'embedsidemenu',
          sidebarExpandedWidth: '200px',
        }}
        menuDataRender={menuData => menuData}
        menuItemRender={(item, defaultDom) => {
          // 如果不是目标菜单项，直接返回原始DOM，保持原有功能
          if (item.path !== '/orderCenter/orderManagement') {
            return defaultDom;
          }

          // 只对目标菜单项添加徽章
          return (
            <div style={{ position: 'relative', width: '100%' }}>
              {defaultDom}
              <Badge
                count={22}
                size="small"
                style={{
                  position: 'absolute',
                  right: '8px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  zIndex: 10,
                  pointerEvents: 'none'
                }}
              />
            </div>
          );
        }}

        {...needProps}
        breadcrumbHomeIcon={false}
        tabsBarHomeIcon={false}
      >
        <ConfigProvider
          getPopupContainer={trigger =>
            trigger && trigger.parentElement ? trigger.parentElement : document.body
          }
        >
          {children}
        </ConfigProvider>
      </ProLayout>
    </Fragment>
  );
};

export default connect(({ settings }) => ({
  settings,
}))(BasicLayout);
