/*
 * @Descripttion:
 * @version:
 * @LastEditors: @LastEditedBy
 * @Date: 2022-10-24 15:01:42
 * @LastEditTime: 2025-08-04 08:59:23
 */
/**
 * Demasia Pro v4 use `demasia-pro-layout` to handle Layout.
 * You can view component api by:
 * http://git.ponshine.fe/demasia/demasia-pro-layout
 */
import ProLayout, { Toolt<PERSON>, SettingDrawer, FixedHeightContent } from 'demasia-pro-layout';
import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'dryad';
import { ConfigProvider } from 'antd';
import RightContent from '@/components/GlobalHeader/RightContent';
import projectSettings from '../../config/projectSettings';
import './BasicLayout.less';
// import title1 from '@/assets/title.png';

const BasicLayout = props => {
  const { dispatch, children, settings, ...needProps } = props;

  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      });
    }
  }, []);

  // 添加菜单徽章的 useEffect
  useEffect(() => {
    const addMenuBadge = () => {
      // 查找目标菜单项
      const menuItems = document.querySelectorAll('.ant-menu-item');
      menuItems.forEach(item => {
        const link = item.querySelector('a');
        if (link && link.getAttribute('href') === '/orderCenter/orderManagement') {
          // 检查是否已经添加了徽章
          if (!item.querySelector('.menu-badge-22')) {
            // 创建徽章元素
            const badge = document.createElement('span');
            badge.className = 'menu-badge-22';
            badge.textContent = '22';
            badge.style.cssText = `
              position: absolute;
              right: 8px;
              top: 50%;
              transform: translateY(-50%);
              background-color: #ff4d4f;
              color: #fff;
              font-size: 10px;
              height: 16px;
              min-width: 16px;
              line-height: 16px;
              border-radius: 8px;
              box-shadow: 0 0 0 1px #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 4px;
              z-index: 10;
              pointer-events: none;
            `;

            // 设置菜单项为相对定位
            item.style.position = 'relative';
            item.appendChild(badge);
          }
        }
      });
    };

    // 延迟执行，确保菜单已经渲染
    const timer = setTimeout(addMenuBadge, 500);

    // 监听 DOM 变化，处理路由切换后的重新渲染
    const observer = new MutationObserver(() => {
      setTimeout(addMenuBadge, 100);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, []);
  return (
    <Fragment>
      <ProLayout
        logo={projectSettings.logo}
        title={projectSettings.title}
        footerRender={false}
        homeIcon={false}
        headerRightContentRender={() => <RightContent />}
        settings={{
          ...settings,
          navLayoutMode: 'embedsidemenu',
          sidebarExpandedWidth: '200px',
        }}
        menuDataRender={menuData => menuData}


        {...needProps}
        breadcrumbHomeIcon={false}
        tabsBarHomeIcon={false}
      >
        <ConfigProvider
          getPopupContainer={trigger =>
            trigger && trigger.parentElement ? trigger.parentElement : document.body
          }
        >
          {children}
        </ConfigProvider>
      </ProLayout>
    </Fragment>
  );
};

export default connect(({ settings }) => ({
  settings,
}))(BasicLayout);
