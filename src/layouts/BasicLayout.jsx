/*
 * @Descripttion:
 * @version:
 * @LastEditors: @LastEditedBy
 * @Date: 2022-10-24 15:01:42
 * @LastEditTime: 2025-08-04 08:50:43
 */
/**
 * Demasia Pro v4 use `demasia-pro-layout` to handle Layout.
 * You can view component api by:
 * http://git.ponshine.fe/demasia/demasia-pro-layout
 */
import ProLayout, { Toolt<PERSON>, SettingDrawer, FixedHeightContent } from 'demasia-pro-layout';
import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'dryad';
import { Icon, ConfigProvider,Badge } from 'antd';
import RightContent from '@/components/GlobalHeader/RightContent';
import MenuBadgeManager from '@/components/MenuBadgeManager';
import projectSettings from '../../config/projectSettings';
import './BasicLayout.less';
// import title1 from '@/assets/title.png';

const BasicLayout = props => {
  const { dispatch, children, settings, ...needProps } = props;
  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      });
    }
  }, []);
  return (
    <Fragment>
      <ProLayout
        logo={projectSettings.logo}
        title={projectSettings.title}
        footerRender={false}
        homeIcon={false}
        headerRightContentRender={() => <RightContent />}
        settings={{
          ...settings,
          navLayoutMode: 'embedsidemenu',
          sidebarExpandedWidth: '200px',
        }}
        menuDataRender={menuData => menuData}
         menuItemRender={(item, defaultDom) => {
        console.log(item,'item',defaultDom,'defaultDom')
        // return defaultDom
        return (
          <div
          >
            <div>
              {item.icon ? <Icon type={item.icon}></Icon> : ''}
              {item.name}
            </div>
            {item.path == '/orderCenter/orderManagement'? (
              <div className={"countWrapper"}>{22 || '--'}</div>
            ) : (
              ''
            )}
          </div>
        );
      }}

        {...needProps}
        breadcrumbHomeIcon={false}
        tabsBarHomeIcon={false}
      >
        <ConfigProvider
          getPopupContainer={trigger =>
            trigger && trigger.parentElement ? trigger.parentElement : document.body
          }
        >
          {children}
        </ConfigProvider>
      </ProLayout>
    </Fragment>
  );
};

export default connect(({ settings }) => ({
  settings,
}))(BasicLayout);
