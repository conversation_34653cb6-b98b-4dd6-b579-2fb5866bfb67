/*
 * @Descripttion:
 * @version:
 * @LastEditors: @LastEditedBy
 * @Date: 2022-10-24 15:01:42
 * @LastEditTime: 2025-08-04 09:17:34
 */
/**
 * Demasia Pro v4 use `demasia-pro-layout` to handle Layout.
 * You can view component api by:
 * http://git.ponshine.fe/demasia/demasia-pro-layout
 */
import ProLayout, { Toolt<PERSON>, SettingDrawer, FixedHeightContent } from 'demasia-pro-layout';
import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'dryad';
import { ConfigProvider, Badge } from 'antd';
import RightContent from '@/components/GlobalHeader/RightContent';
import projectSettings from '../../config/projectSettings';
import request from '@/utils/request';
import './BasicLayout.less';
// import title1 from '@/assets/title.png';

const BasicLayout = props => {
  const { dispatch, children, settings, ...needProps } = props;
  const [unReadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      });
    }
    getUnreadCount()
  }, []);
  // 获取未读消息数量
  const getUnreadCount = () => {
    setUnreadCount(222)
    return
    request('/api/order/orderManage/getUnreadCount', {
      method: 'POST',
    }).then(res => {
      if (res.code == 200) {
        setUnreadCount(res.resData || 0);
      }
    });
  };


  return (
    <Fragment>
      <ProLayout
        logo={projectSettings.logo}
        title={projectSettings.title}
        footerRender={false}
        homeIcon={false}
        headerRightContentRender={() => <RightContent />}
        settings={{
          ...settings,
          navLayoutMode: 'embedsidemenu',
          sidebarExpandedWidth: '200px',
        }}
        menuDataRender={menuData => menuData}
        postMenuData={(menuData) => {
          const processMenuData = (menus) => {
            return menus.map(menu => {
              if (menu.path === '/orderCenter/orderManagement') {
                return {
                  ...menu,
                  name: (

                    <Badge
                      count={unReadCount}
                      size="small"
                      style={{marginRight: '-20px'}}
                      showZero
                      overflowCount={9999}
                    >
                      <span style={{ position: 'relative', display: 'inline-block', width: '100%' }}>
                        {menu.name}
                        {/* <span
                        style={{  
                          position: 'absolute',
                          right: '8px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          backgroundColor: '#ff4d4f',
                          color: '#fff',
                          fontSize: '10px',
                          height: '16px',
                          minWidth: '16px',
                          lineHeight: '16px',
                          borderRadius: '8px',
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          padding: '0 4px',
                          zIndex: 10,
                          pointerEvents: 'none'
                        }}
                      >
                        {unReadCount}
                      </span> */}
                      </span>
                    </Badge>
                  )
                };
              }
              if (menu.children) {
                return {
                  ...menu,
                  children: processMenuData(menu.children)
                };
              }
              return menu;
            });
          };
          return processMenuData(menuData);
        }}
        {...needProps}
        breadcrumbHomeIcon={false}
        tabsBarHomeIcon={false}
      >
        <ConfigProvider
          getPopupContainer={trigger =>
            trigger && trigger.parentElement ? trigger.parentElement : document.body
          }
        >
          {children}
        </ConfigProvider>
      </ProLayout>
    </Fragment>
  );
};

export default connect(({ settings }) => ({
  settings,
}))(BasicLayout);
