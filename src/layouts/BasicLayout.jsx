/*
 * @Descripttion:
 * @version:
 * @LastEditors: @LastEditedBy
 * @Date: 2022-10-24 15:01:42
 * @LastEditTime: 2025-08-04 09:01:11
 */
/**
 * Demasia Pro v4 use `demasia-pro-layout` to handle Layout.
 * You can view component api by:
 * http://git.ponshine.fe/demasia/demasia-pro-layout
 */
import ProLayout, { Toolt<PERSON>, SettingDrawer, FixedHeightContent } from 'demasia-pro-layout';
import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'dryad';
import { ConfigProvider, Badge } from 'antd';
import RightContent from '@/components/GlobalHeader/RightContent';
import projectSettings from '../../config/projectSettings';
import './BasicLayout.less';
// import title1 from '@/assets/title.png';

const BasicLayout = props => {
  const { dispatch, children, settings, ...needProps } = props;

  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      });
    }
  }, []);


  return (
    <Fragment>
      <ProLayout
        logo={projectSettings.logo}
        title={projectSettings.title}
        footerRender={false}
        homeIcon={false}
        headerRightContentRender={() => <RightContent />}
        settings={{
          ...settings,
          navLayoutMode: 'embedsidemenu',
          sidebarExpandedWidth: '200px',
        }}
        menuDataRender={menuData => menuData}
        menuItemRender={(item, defaultDom) => {
          // 对于非目标菜单项，直接返回原始DOM
          if (item.path !== '/orderCenter/orderManagement') {
            return defaultDom;
          }

          // 对于目标菜单项，我们需要克隆原始元素并保持所有原有属性
          const originalElement = React.isValidElement(defaultDom) ? defaultDom : <span>{defaultDom}</span>;

          // 使用 React.cloneElement 来保持原有的所有属性和事件处理器
          return React.cloneElement(originalElement, {
            ...originalElement.props,
            style: {
              ...originalElement.props.style,
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              width: '100%'
            },
            children: (
              <>
                {originalElement.props.children}
                <Badge
                  count={22}
                  size="small"
                  style={{
                    position: 'absolute',
                    right: '8px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    zIndex: 10
                  }}
                />
              </>
            )
          });
        }}
        {...needProps}
        breadcrumbHomeIcon={false}
        tabsBarHomeIcon={false}
      >
        <ConfigProvider
          getPopupContainer={trigger =>
            trigger && trigger.parentElement ? trigger.parentElement : document.body
          }
        >
          {children}
        </ConfigProvider>
      </ProLayout>
    </Fragment>
  );
};

export default connect(({ settings }) => ({
  settings,
}))(BasicLayout);
